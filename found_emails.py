#!/usr/bin/env python3
"""
Extract and compile emails found from web search results
"""

import re
from datetime import datetime

def extract_emails_from_search_results():
    """Extract emails from the web search results"""
    
    # Search results content
    search_results = """
    - [DIPLOMATIC & CONSULAR LIST](https://www.mfa.gov.sg/Overseas-Missions/-/media/D74B3129AEFA44BB8FC411746F005489.ashx)
      Aug 29, 2019 ... Mdm Su Wei <PERSON>. Page 216. 207. THAILAND. Royal Thai Embassy. Diplomatic Relations established on 20 Sep 1965. Chancery: 370 Orchard Road.

    - [APPLIED STATISTICS](https://www.imstat.org/publications/aoas/aoas_19_1/aoas_19_1.pdf)
      Mar 1, 2025 ... HUANG, J., HOROWITZ, J. L. and WEI, F. (2010). Variable selection in ... In Mining Text Data 163–222. Springer, US. ALLISON, B ...

    - [ANNUAL MEETING](https://higherlogicdownload.s3.amazonaws.com/NCME/4b7590fc-3903-444d-b89d-c45b7fa3da3f/UploadedImages/2025_Annual_Meeting/2025_NCME_Annual_Meeting_V6.pdf)
      Apr 24, 2025 ... Visit PearsonAssessments.com/Learning-Suite to learn more. Copyright © 2025 Pearson Education, Inc. or its affiliates. All rights reserved.

    - [Heterogeneous Catalysis for Carbon Dioxide Mediated Hydrogen ...](https://onlinelibrary.wiley.com/doi/abs/10.1002/aenm.202200817)
      Jun 7, 2022 ... Through reversible CO2/H2-formic acid (FA) reactions, a "carbon neutral" hydrogen storage system can be constructed.

    - ["Local/indigenous people and sustainable development in ...](https://www.globalgeoparksnetwork.org/sites/default/files/2024-11/APGN%202024%20ABSTRACTS%20BOOK.pdf)
      Email: <EMAIL>; <EMAIL>. Page 70. Oral session 3 - Heritage inventory, protection and sustainable use. THE 8TH ASIA PACIFIC GEOPARKS NETWORK ...

    - [12th International Conference on Fiber-Reinforced Polymer (FRP ...](https://cice2025.org/wp-content/uploads/2025/07/CICE-2025-Book-of-Abstracts_web.pdf)
      Jul 14, 2025 ... HUANG, Silu; WEI, Yang; YAN, Libo. Development of synthetic fiber ... email: a <EMAIL>, b <EMAIL>. ABSTRACT. The ...

    - [Volume 13, 2018 - International Journal of Electrochemical Science](http://www.electrochemsci.org/list18.htm)
      Faculty of Metallurgical and Energy Engineering, Kunming University of Science and Technology, Kunming 650093, China. *E-mail: <EMAIL> · Effect of ...

    - [<NAME_EMAIL> carlos.ro.rod@gmail ...](https://cityclerk.lacity.org/onlinedocs/2022/22-0268_misc_4_05-17-23.pdf)
      ... <EMAIL> · <EMAIL> · <EMAIL> ... txt.att.net · <EMAIL> · <EMAIL> · sarah.dusseault@lacity ...

    - [CHAMORRO LAND TRUST COMMISSION](https://dlm.guam.gov/wp-dlm-content/uploads/2024/03/POST-CLTC-MEETING-PACKET-15FEB2024-5.pdf)
      Jul 3, 2024 ... paulyu.py@gmail. com. Master. Electrician. ME-0723-0023. Page 50. 2023-2025. NAME. LIC #. CONTACT #. MAILING. ADDRESS. PHYSICAL. ADDRESS. EMAIL ...

    - [Critical Review, Recent Updates on Zeolitic Imidazolate Framework ...](https://advanced.onlinelibrary.wiley.com/doi/abs/10.1002/adma.202107072)
      Nov 30, 2021 ... Metal-organic frameworks (MOF) are promising candidates as precursor materials in the development of highly efficient electrocatalysts for ...
    """
    
    # Email regex pattern
    email_pattern = re.compile(
        r'\b[A-Za-z0-9]([A-Za-z0-9._%-]*[A-Za-z0-9])?@[A-Za-z0-9]([A-Za-z0-9.-]*[A-Za-z0-9])?\.[A-Za-z]{2,}\b'
    )
    
    emails = email_pattern.findall(search_results)
    valid_emails = []
    
    # Target domains and names
    target_domains = ['gmail.com', '163.com', 'qq.com', '128.com', '126.com']
    target_names = ['huang', 'hyun', 'wei', 'yu', 'zhang']
    
    for email_match in emails:
        # Handle tuple results from regex groups
        if isinstance(email_match, tuple):
            email = ''.join(email_match)
        else:
            email = email_match
        
        email = email.lower().strip()
        
        # Check if email contains target domains or names
        if (any(domain in email for domain in target_domains) or
            any(name in email for name in target_names)):
            
            # Additional validation
            if len(email) >= 5 and len(email) <= 254 and email.count('@') == 1:
                valid_emails.append(email)
    
    # Also manually add the emails I can see in the text
    manual_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    # Combine and deduplicate
    all_emails = list(set(valid_emails + manual_emails))
    
    return all_emails

def save_emails_to_files(emails):
    """Save emails to both TXT and CSV files"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save to TXT
    txt_filename = f"scraped_emails/found_emails_{timestamp}.txt"
    with open(txt_filename, 'w', encoding='utf-8') as f:
        f.write(f"# Emails found from web search results\n")
        f.write(f"# Extracted on: {datetime.now().isoformat()}\n")
        f.write(f"# Total emails: {len(emails)}\n")
        f.write(f"# Search criteria: @gmail.com, @163.com, @qq.com, @128.com, huang, hyun, wei\n\n")
        
        for email in sorted(emails):
            f.write(f"{email}\n")
    
    # Save to CSV
    csv_filename = f"scraped_emails/found_emails_{timestamp}.csv"
    import csv
    with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Email', 'Domain', 'Type', 'Timestamp'])
        
        for email in sorted(emails):
            domain = email.split('@')[1] if '@' in email else ''
            email_type = 'Target Domain' if any(d in domain for d in ['gmail.com', '163.com', 'qq.com', '128.com', '126.com']) else 'Target Name'
            timestamp_str = datetime.now().isoformat()
            writer.writerow([email, domain, email_type, timestamp_str])
    
    return txt_filename, csv_filename

def analyze_emails(emails):
    """Analyze the found emails"""
    print("📊 EMAIL ANALYSIS:")
    print("=" * 50)
    
    # Domain breakdown
    domains = {}
    for email in emails:
        domain = email.split('@')[1] if '@' in email else 'unknown'
        domains[domain] = domains.get(domain, 0) + 1
    
    print("🏢 Domain breakdown:")
    for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True):
        print(f"   - {domain}: {count} emails")
    
    # Target domain analysis
    target_domains = ['gmail.com', '163.com', 'qq.com', '128.com', '126.com']
    target_emails = [email for email in emails if any(domain in email for domain in target_domains)]
    
    print(f"\n🎯 Target domain emails ({len(target_emails)} found):")
    for email in sorted(target_emails):
        print(f"   - {email}")
    
    # Name analysis
    target_names = ['huang', 'hyun', 'wei', 'yu', 'zhang']
    name_emails = [email for email in emails if any(name in email.lower() for name in target_names)]
    
    if name_emails:
        print(f"\n👤 Emails with target names ({len(name_emails)} found):")
        for email in sorted(name_emails):
            print(f"   - {email}")

def main():
    """Main function"""
    print("🔍 EMAIL EXTRACTION FROM WEB SEARCH RESULTS")
    print("=" * 60)
    
    # Extract emails
    emails = extract_emails_from_search_results()
    
    if emails:
        print(f"✅ Found {len(emails)} unique emails:")
        for i, email in enumerate(sorted(emails), 1):
            print(f"   {i:2d}. {email}")
        
        # Save to files
        txt_file, csv_file = save_emails_to_files(emails)
        
        print(f"\n💾 Files saved:")
        print(f"   - TXT: {txt_file}")
        print(f"   - CSV: {csv_file}")
        
        # Analyze emails
        print(f"\n")
        analyze_emails(emails)
        
        print(f"\n📋 SUMMARY:")
        print(f"   - Total unique emails: {len(emails)}")
        print(f"   - Target domains: gmail.com, 163.com, qq.com, 128.com, 126.com")
        print(f"   - Target names: huang, hyun, wei, yu, zhang")
        print(f"   - Source: Web search results")
        
    else:
        print("❌ No emails found in the search results")

if __name__ == "__main__":
    main()
