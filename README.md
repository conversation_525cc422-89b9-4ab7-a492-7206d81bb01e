# 🔍 Google Email Scraper

A powerful and advanced email scraping tool that extracts email addresses from Google search results with proxy support, user-agent rotation, and multiple scraping methods.

## ✨ Features

- **Multiple Scraping Methods**: Requests, Selenium, CloudScraper, and async HTTP clients
- **Proxy Rotation**: Support for HTTP/HTTPS/SOCKS proxies to avoid IP blocking
- **User-Agent Rotation**: Randomized headers to appear as different users
- **Async Support**: Fast concurrent scraping with aiohttp
- **Email Validation**: Advanced email filtering and validation
- **Multiple Output Formats**: TXT, CSV, and JSON with metadata
- **Rate Limiting**: Configurable delays and request limits
- **Comprehensive Logging**: Detailed logs for monitoring and debugging
- **Error Handling**: Robust error handling with retry mechanisms

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the project files
# Install Python dependencies
pip install -r requirements.txt
```

### 2. Basic Usage

```bash
# Interactive mode
python run_scraper.py

# Command line mode
python run_scraper.py --query "contact email site:company.com" --pages 3 --links 5

# Async mode with proxy
python run_scraper.py --query "sales email" --async --proxy --concurrent 5
```

### 3. Configuration

Edit `config.py` to customize:
- Proxy servers
- User agents
- Email validation rules
- Output formats
- Rate limiting settings

## 📖 Usage Examples

### Basic Email Scraping
```bash
python run_scraper.py --query "contact email marketing agency" --pages 5
```

### Target Specific Domains
```bash
python run_scraper.py --query "support email site:*.edu" --pages 3 --links 10
```

### Fast Async Scraping
```bash
python run_scraper.py --query "info email startup" --async --concurrent 10
```

### With Proxy Rotation
```bash
python run_scraper.py --query "sales email" --proxy --pages 5
```

## 🔧 Configuration

### Proxy Setup
Add your proxy servers to `config.py`:

```python
PROXY_LIST = [
    "http://proxy1.example.com:8080",
    "http://proxy2.example.com:8080",
    "socks5://proxy3.example.com:1080",
]
```

### Search Query Examples
- `"contact email site:company.com"` - Target specific domain
- `"sales email marketing agency"` - Industry-specific search
- `"support email site:*.edu"` - Educational institutions
- `"info email startup"` - Startup companies
- `"contact us email site:github.com"` - Specific platform

## 📊 Output Formats

### TXT Format
```
<EMAIL>
<EMAIL>
<EMAIL>
```

### CSV Format
```csv
Email,Domain,Source_URL,Timestamp
<EMAIL>,domain.com,https://example.com,2024-01-01T12:00:00
```

### JSON Format
```json
{
  "metadata": {
    "scraping_timestamp": "2024-01-01T12:00:00",
    "total_emails": 50,
    "statistics": {...}
  },
  "emails": [
    {
      "email": "<EMAIL>",
      "domain": "example.com",
      "source_url": "https://example.com/contact"
    }
  ]
}
```

## 🛠️ Advanced Features

### Custom User Agents
```python
CUSTOM_USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36...",
]
```

### Email Validation Rules
```python
VALID_EMAIL_DOMAINS = {
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'
}

EXCLUDED_EMAIL_PATTERNS = [
    'noreply', 'no-reply', 'donotreply', 'example.com'
]
```

### Rate Limiting
```python
DEFAULT_DELAY_RANGE = (2, 5)  # 2-5 seconds between requests
REQUESTS_PER_MINUTE = 30
CONCURRENT_REQUESTS = 3
```

## 📁 Project Structure

```
EMAIL-LEADS/
├── google_email_scraper.py    # Main synchronous scraper
├── advanced_scraper.py        # Advanced async scraper
├── run_scraper.py            # CLI interface
├── config.py                 # Configuration settings
├── requirements.txt          # Python dependencies
├── README.md                 # This file
└── scraped_emails/           # Output directory
    ├── emails_20240101_120000.txt
    ├── emails_20240101_120000.csv
    └── emails_20240101_120000.json
```

## ⚡ Performance Tips

1. **Use Async Mode**: Much faster for large-scale scraping
2. **Proxy Rotation**: Prevents IP blocking and rate limiting
3. **Reasonable Delays**: Don't set delays too low to avoid detection
4. **Target Specific Domains**: Use `site:` operator for better results
5. **Monitor Logs**: Check logs for errors and optimization opportunities

## 🔒 Legal and Ethical Considerations

- **Respect robots.txt**: Check website policies before scraping
- **Rate Limiting**: Don't overwhelm servers with requests
- **Data Privacy**: Handle collected emails responsibly
- **Terms of Service**: Comply with Google's and target websites' ToS
- **Local Laws**: Ensure compliance with data protection regulations

## 🐛 Troubleshooting

### Common Issues

1. **No emails found**:
   - Try different search queries
   - Increase number of pages
   - Check if target sites block scraping

2. **Proxy errors**:
   - Verify proxy servers are working
   - Check proxy format (http://ip:port)
   - Try different proxy providers

3. **Selenium issues**:
   - Update Chrome browser
   - Check ChromeDriver compatibility
   - Try headless mode

4. **Rate limiting**:
   - Increase delays between requests
   - Use proxy rotation
   - Reduce concurrent requests

### Debug Mode
Enable debug logging in `config.py`:
```python
LOG_LEVEL = "DEBUG"
```

## 📈 Statistics and Monitoring

The scraper provides detailed statistics:
- Total emails found
- Pages scraped vs failed
- Scraping duration
- Unique domains
- Request success rate

## 🤝 Contributing

Feel free to contribute by:
- Adding new scraping methods
- Improving email validation
- Adding new output formats
- Enhancing proxy support
- Fixing bugs and issues

## ⚠️ Disclaimer

This tool is for educational and legitimate business purposes only. Users are responsible for:
- Complying with applicable laws and regulations
- Respecting website terms of service
- Using collected data ethically and responsibly
- Obtaining necessary permissions for data collection

The developers are not responsible for misuse of this tool.

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error details
3. Ensure all dependencies are installed correctly
4. Verify your configuration settings

Happy scraping! 🎉
