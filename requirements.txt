# Core scraping libraries
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Selenium and browser automation
selenium>=4.15.0
undetected-chromedriver>=3.5.0
webdriver-manager>=4.0.0

# User agent and proxy support
fake-useragent>=1.4.0
scrapy>=2.11.0
scrapy-rotating-proxies>=0.6.2
scrapy-user-agents>=0.1.1

# Advanced HTTP clients
httpx>=0.25.0
aiohttp>=3.8.0
cloudscraper>=1.2.60
requests-html>=0.10.0

# Async support
asyncio
aiofiles>=23.0.0

# Data processing and validation
pandas>=2.0.0
validators>=0.22.0

# Output formats
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# Utility libraries
python-dotenv>=1.0.0
colorama>=0.4.6
tqdm>=4.66.0
click>=8.1.0

# Optional: For advanced features
# playwright>=1.40.0  # Alternative to Selenium
# pyppeteer>=1.0.0    # Puppeteer for Python
# requests-oauthlib>=1.3.0  # OAuth support
# pysocks>=1.7.0      # SOCKS proxy support
