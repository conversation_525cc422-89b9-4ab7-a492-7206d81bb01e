"""
Configuration file for Google Email Scraper
"""

# Proxy Configuration
# Add your proxy servers here in the format "protocol://ip:port"
# You can get proxies from services like:
# - ProxyMesh, Bright Data, Oxylabs, etc.
# - Free proxy lists (less reliable)

PROXY_LIST = [
    # Example proxies (replace with your own):
    # "http://proxy1.example.com:8080",
    # "http://proxy2.example.com:8080",
    # "socks5://proxy3.example.com:1080",
    
    # Free proxy examples (may not work reliably):
    # "http://***************:80",
    # "http://***************:80",
    
    # Add your working proxies here
]

# User Agent Configuration
CUSTOM_USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
]

# Scraping Configuration
DEFAULT_DELAY_RANGE = (2, 5)  # Min and max delay between requests in seconds
DEFAULT_TIMEOUT = 15  # Request timeout in seconds
MAX_RETRIES = 3  # Maximum number of retries for failed requests

# Email Validation Configuration
VALID_EMAIL_DOMAINS = {
    # Popular email providers
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
    'aol.com', 'icloud.com', 'protonmail.com', 'mail.com',
    'zoho.com', 'yandex.com', 'fastmail.com', 'tutanota.com',
    
    # Business email domains (add more as needed)
    'company.com', 'business.com', 'enterprise.com',
}

# Domains to exclude (spam/fake emails)
EXCLUDED_EMAIL_PATTERNS = [
    'noreply', 'no-reply', 'donotreply', 'do-not-reply',
    'example.com', 'test.com', 'sample.com', 'demo.com',
    'placeholder', 'dummy', 'fake', 'invalid'
]

# Google Search Configuration
GOOGLE_SEARCH_DOMAINS = [
    "https://www.google.com",
    "https://www.google.co.uk", 
    "https://www.google.ca",
    "https://www.google.com.au",
]

# Output Configuration
OUTPUT_FORMATS = ['txt', 'csv', 'json']
DEFAULT_OUTPUT_DIR = "scraped_emails"

# Logging Configuration
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
LOG_FILE = "scraper.log"
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"

# Advanced Configuration
USE_SELENIUM_FALLBACK = True  # Use Selenium if requests fail
USE_CLOUDSCRAPER_FALLBACK = True  # Use CloudScraper for Cloudflare protection
ENABLE_JAVASCRIPT_RENDERING = True  # Enable JS rendering with Selenium

# Rate Limiting
REQUESTS_PER_MINUTE = 30  # Maximum requests per minute
CONCURRENT_REQUESTS = 3  # Number of concurrent requests

# Search Query Templates
SEARCH_QUERY_TEMPLATES = [
    'contact email site:{domain}',
    'email address site:{domain}',
    '"{keyword}" email contact',
    'contact us site:{domain}',
    'support email site:{domain}',
    'info email site:{domain}',
    'sales email site:{domain}',
]

# File Extensions to Scrape
SCRAPABLE_EXTENSIONS = [
    '.html', '.htm', '.php', '.asp', '.aspx', '.jsp',
    '.txt', '.pdf'  # Note: PDF scraping requires additional libraries
]

# Headers for different request types
REQUEST_HEADERS = {
    'default': {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
    },
    'ajax': {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'X-Requested-With': 'XMLHttpRequest',
    }
}

# Chrome Options for Selenium
CHROME_OPTIONS = [
    '--no-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--window-size=1920,1080',
    '--disable-blink-features=AutomationControlled',
    '--disable-extensions',
    '--disable-plugins',
    '--disable-images',  # Faster loading
    '--disable-javascript',  # Can be enabled if needed
]

# Selenium Configuration
SELENIUM_TIMEOUT = 20
SELENIUM_IMPLICIT_WAIT = 10
SELENIUM_PAGE_LOAD_TIMEOUT = 30

# Error Handling
MAX_CONSECUTIVE_FAILURES = 5  # Stop after this many consecutive failures
RETRY_DELAY = 5  # Seconds to wait before retrying failed requests

# Data Validation
MIN_EMAIL_LENGTH = 5
MAX_EMAIL_LENGTH = 254  # RFC 5321 limit
VALIDATE_EMAIL_SYNTAX = True
REMOVE_DUPLICATES = True

# Export Configuration
EXPORT_METADATA = True  # Include timestamp, source URL, etc.
SORT_RESULTS = True  # Sort emails alphabetically
INCLUDE_STATISTICS = True  # Include scraping statistics in output
