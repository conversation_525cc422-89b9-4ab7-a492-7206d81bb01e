#!/usr/bin/env python3
"""
Quick test script to verify the Google URL and extract initial data
"""

import requests
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
import re
from urllib.parse import unquote

def test_google_url():
    """Test the Google URL and extract basic information"""
    
    google_url = "https://www.google.com/search?q=@gmail.com,+@<EMAIL><EMAIL>+huang,+hyun,+wei+txt+2025&sca_esv=e7bf22627bcd1c5c&sxsrf=AE3TifNnUr3tV4AY0Aw_D1qB8yJA_rXNGA:1757431062814&ei=FkXAaOq-MamAhbIPz_uG-QM&start=60&sa=N&sstk=Ac65TH6TYToOn1W4LVNuOKsO2eTNSaF_lghNz8xgFUWd37AoHg2sGux2ExZ2jB_VagKJ7t1o7QLsnE6jnwEoloJnj4nEd-u2BJo2EER5lTf-HrXdgv3PFgpInQ7CqZ1KN3E9dKxwNn7IugSpiDk2-U9C3VikKtPSdq0jMV46GlNptDt1EyG1Kv1N9XIl4Paejcow8MNWyiQtmKrS2EKgmxOXHC2zvygg&ved=2ahUKEwjqod3x_MuPAxUpQEEAHc-9IT84KBDy0wN6BAgJEA8&biw=1280&bih=673&dpr=2"
    
    print("🔍 Testing Google URL...")
    print(f"URL: {google_url}")
    print("-" * 80)
    
    # Setup headers
    ua = UserAgent()
    headers = {
        'User-Agent': ua.random,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        # Make request
        print("📡 Making request to Google...")
        response = requests.get(google_url, headers=headers, timeout=15)
        response.raise_for_status()
        
        print(f"✅ Response received: {response.status_code}")
        print(f"📄 Content length: {len(response.text)} characters")
        
        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract search results
        print("\n🔍 Analyzing search results...")
        
        # Look for result links
        result_links = []
        selectors = [
            'div.g a[href]',
            'h3 a[href]', 
            'a[href^="/url?q="]',
            '.yuRUbf a[href]',
            '.kCrYT a[href]',
        ]
        
        for selector in selectors:
            for link in soup.select(selector):
                href = link.get('href', '')
                
                if href.startswith('/url?q='):
                    try:
                        actual_url = href.split('/url?q=')[1].split('&')[0]
                        actual_url = unquote(actual_url)
                        if actual_url.startswith('http') and 'google.com' not in actual_url:
                            result_links.append(actual_url)
                    except (IndexError, ValueError):
                        continue
                elif href.startswith('http') and 'google.com' not in href:
                    result_links.append(href)
        
        # Remove duplicates
        unique_links = list(dict.fromkeys(result_links))
        
        print(f"📊 Found {len(unique_links)} unique result links:")
        for i, link in enumerate(unique_links[:10], 1):  # Show first 10
            print(f"   {i:2d}. {link}")
        
        if len(unique_links) > 10:
            print(f"   ... and {len(unique_links) - 10} more links")
        
        # Look for emails in the search results page itself
        email_pattern = re.compile(
            r'\b[A-Za-z0-9]([A-Za-z0-9._%-]*[A-Za-z0-9])?@[A-Za-z0-9]([A-Za-z0-9.-]*[A-Za-z0-9])?\.[A-Za-z]{2,}\b'
        )
        
        emails_found = email_pattern.findall(response.text)
        target_domains = ['gmail.com', '163.com', 'qq.com', '128.com']
        target_names = ['huang', 'hyun', 'wei']
        
        valid_emails = []
        for email_match in emails_found:
            if isinstance(email_match, tuple):
                email = ''.join(email_match)
            else:
                email = email_match
            
            email = email.lower().strip()
            
            if (any(domain in email for domain in target_domains) or
                any(name in email for name in target_names)):
                valid_emails.append(email)
        
        # Remove duplicates
        unique_emails = list(dict.fromkeys(valid_emails))
        
        print(f"\n📧 Found {len(unique_emails)} target emails on search page:")
        for email in unique_emails:
            print(f"   - {email}")
        
        # Check for next page
        next_links = soup.find_all('a', {'aria-label': 'Next page'})
        if not next_links:
            next_links = soup.find_all('a', string=re.compile(r'Next|下一页'))
        
        next_page_url = None
        for link in next_links:
            href = link.get('href')
            if href:
                if href.startswith('/search'):
                    next_page_url = f"https://www.google.com{href}"
                    break
                elif href.startswith('http'):
                    next_page_url = href
                    break
        
        if next_page_url:
            print(f"\n➡️  Next page URL found: {next_page_url}")
        else:
            print("\n❌ No next page URL found")
        
        # Summary
        print(f"\n📊 SUMMARY:")
        print(f"   - Search results found: {len(unique_links)}")
        print(f"   - Emails on search page: {len(unique_emails)}")
        print(f"   - Next page available: {'Yes' if next_page_url else 'No'}")
        
        return {
            'success': True,
            'result_links': unique_links,
            'emails_found': unique_emails,
            'next_page_url': next_page_url
        }
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return {'success': False, 'error': str(e)}
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """Main function"""
    print("🧪 Quick Test for Google URL Email Scraper")
    print("=" * 80)
    
    result = test_google_url()
    
    if result['success']:
        print("\n✅ Test completed successfully!")
        print("\n🚀 You can now run the full scraper with:")
        print("   python run_google_url_scraper.py")
    else:
        print(f"\n❌ Test failed: {result['error']}")
        print("\n💡 Suggestions:")
        print("   - Check your internet connection")
        print("   - Try running the test again")
        print("   - The Google URL might have expired")

if __name__ == "__main__":
    main()
