#!/usr/bin/env python3
"""
Simple CLI interface for the Google Email Scraper
"""

import asyncio
import sys
import argparse
from pathlib import Path
from advanced_scraper import AdvancedEmailScraper
from google_email_scraper import GoogleEmailScraper
from config import PROXY_LIST

def print_banner():
    """Print application banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🔍 GOOGLE EMAIL SCRAPER 🔍                ║
    ║                                                              ║
    ║  Advanced email scraping tool with proxy support            ║
    ║  and user-agent rotation for lead generation                ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_help():
    """Print usage help"""
    help_text = """
    📖 USAGE EXAMPLES:
    
    Basic usage:
    python run_scraper.py
    
    With command line arguments:
    python run_scraper.py --query "contact email site:company.com" --pages 5 --links 10
    
    With proxy support:
    python run_scraper.py --query "sales email" --proxy --async
    
    Advanced async mode:
    python run_scraper.py --query "support email" --async --concurrent 5
    
    📋 SEARCH QUERY EXAMPLES:
    - "contact email site:company.com"
    - "sales email marketing agency"
    - "support email site:*.edu"
    - "info email startup"
    - "contact us email site:github.com"
    
    💡 TIPS:
    - Use site: operator to target specific domains
    - Add industry keywords for better targeting
    - Use quotes for exact phrase matching
    - Start with fewer pages for testing
    """
    print(help_text)

def get_user_input():
    """Get user input interactively"""
    print("\n🔧 CONFIGURATION")
    print("=" * 50)
    
    # Search query
    query = input("Enter your search query: ").strip()
    if not query:
        query = "contact email"
        print(f"Using default query: '{query}'")
    
    # Number of pages
    try:
        pages = int(input("Number of Google search pages (default 3): ") or "3")
    except ValueError:
        pages = 3
        print(f"Using default pages: {pages}")
    
    # Links per page
    try:
        links = int(input("Max links per search page (default 5): ") or "5")
    except ValueError:
        links = 5
        print(f"Using default links per page: {links}")
    
    # Proxy usage
    use_proxy = input("Use proxy rotation? (y/n, default n): ").lower().startswith('y')
    
    # Scraper mode
    use_async = input("Use async mode for faster scraping? (y/n, default y): ").lower()
    use_async = not use_async.startswith('n')
    
    # Concurrent requests (for async mode)
    concurrent = 3
    if use_async:
        try:
            concurrent = int(input("Concurrent requests (default 3): ") or "3")
        except ValueError:
            concurrent = 3
    
    return {
        'query': query,
        'pages': pages,
        'links': links,
        'use_proxy': use_proxy,
        'use_async': use_async,
        'concurrent': concurrent
    }

async def run_async_scraper(config):
    """Run the async scraper"""
    print(f"\n🚀 Starting ASYNC scraper...")
    print(f"Query: {config['query']}")
    print(f"Pages: {config['pages']}")
    print(f"Links per page: {config['links']}")
    print(f"Concurrent requests: {config['concurrent']}")
    print(f"Proxy rotation: {'Enabled' if config['use_proxy'] else 'Disabled'}")
    print("-" * 60)
    
    scraper = AdvancedEmailScraper(
        use_proxy=config['use_proxy'],
        proxy_list=PROXY_LIST if config['use_proxy'] else None
    )
    
    try:
        results = await scraper.scrape_google_search_async(
            query=config['query'],
            num_pages=config['pages'],
            max_links_per_page=config['links'],
            concurrent_requests=config['concurrent']
        )
        
        print_results(results)
        
    except KeyboardInterrupt:
        print("\n⚠️ Scraping interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during async scraping: {e}")

def run_sync_scraper(config):
    """Run the synchronous scraper"""
    print(f"\n🚀 Starting SYNC scraper...")
    print(f"Query: {config['query']}")
    print(f"Pages: {config['pages']}")
    print(f"Links per page: {config['links']}")
    print(f"Proxy rotation: {'Enabled' if config['use_proxy'] else 'Disabled'}")
    print("-" * 60)
    
    scraper = GoogleEmailScraper(
        use_proxy=config['use_proxy'],
        proxy_list=PROXY_LIST if config['use_proxy'] else None,
        delay_range=(2, 5)
    )
    
    try:
        results = scraper.scrape_google_search(
            query=config['query'],
            num_pages=config['pages'],
            max_links_per_page=config['links']
        )
        
        print_results(results)
        
    except KeyboardInterrupt:
        print("\n⚠️ Scraping interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during sync scraping: {e}")

def print_results(results):
    """Print scraping results"""
    print("\n✅ SCRAPING COMPLETED!")
    print("=" * 50)
    print(f"📧 Total emails found: {results['count']}")
    
    if results['count'] > 0:
        # Print statistics if available
        if 'stats' in results:
            stats = results['stats']
            duration = (stats['end_time'] - stats['start_time']).total_seconds()
            print(f"⏱️  Scraping duration: {duration:.1f} seconds")
            print(f"🌐 Pages scraped: {stats['pages_scraped']}")
            print(f"❌ Pages failed: {stats['pages_failed']}")
            print(f"🔗 Total requests: {stats['total_requests']}")
            print(f"🏢 Unique domains: {stats['unique_domains']}")
        
        # Print file locations
        if 'files' in results:
            print(f"\n💾 Files saved:")
            for format_type, file_path in results['files'].items():
                print(f"   - {format_type.upper()}: {file_path}")
        elif 'txt_file' in results:
            print(f"\n💾 Files saved:")
            print(f"   - TXT: {results.get('txt_file', 'N/A')}")
            print(f"   - CSV: {results.get('csv_file', 'N/A')}")
        
        # Preview emails
        print(f"\n📋 Email preview (first 10):")
        emails = results['emails'][:10]
        for i, email in enumerate(emails, 1):
            print(f"   {i:2d}. {email}")
        
        if results['count'] > 10:
            print(f"   ... and {results['count'] - 10} more emails")
        
        # Domain breakdown
        if results['emails']:
            domains = {}
            for email in results['emails']:
                domain = email.split('@')[1]
                domains[domain] = domains.get(domain, 0) + 1
            
            print(f"\n🏢 Top domains found:")
            sorted_domains = sorted(domains.items(), key=lambda x: x[1], reverse=True)
            for domain, count in sorted_domains[:5]:
                print(f"   - {domain}: {count} emails")
    else:
        print("❌ No emails found. Try:")
        print("   - Different search queries")
        print("   - More search pages")
        print("   - Different target websites")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Google Email Scraper - Extract emails from Google search results",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--query', '-q', type=str, help='Search query')
    parser.add_argument('--pages', '-p', type=int, default=3, help='Number of search pages')
    parser.add_argument('--links', '-l', type=int, default=5, help='Max links per page')
    parser.add_argument('--proxy', action='store_true', help='Use proxy rotation')
    parser.add_argument('--async', dest='use_async', action='store_true', help='Use async mode')
    parser.add_argument('--sync', dest='use_sync', action='store_true', help='Force sync mode')
    parser.add_argument('--concurrent', '-c', type=int, default=3, help='Concurrent requests (async mode)')
    parser.add_argument('--help-examples', action='store_true', help='Show usage examples')
    
    args = parser.parse_args()
    
    # Print banner
    print_banner()
    
    # Show help examples if requested
    if args.help_examples:
        print_help()
        return
    
    # Get configuration
    if args.query:
        config = {
            'query': args.query,
            'pages': args.pages,
            'links': args.links,
            'use_proxy': args.proxy,
            'use_async': args.use_async and not args.use_sync,
            'concurrent': args.concurrent
        }
    else:
        config = get_user_input()
    
    # Validate proxy list
    if config['use_proxy'] and not PROXY_LIST:
        print("⚠️  Warning: Proxy rotation enabled but no proxies configured in config.py")
        print("   Add proxy servers to PROXY_LIST in config.py")
        config['use_proxy'] = False
    
    # Run scraper
    try:
        if config['use_async']:
            asyncio.run(run_async_scraper(config))
        else:
            run_sync_scraper(config)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
