#!/usr/bin/env python3
"""
Script to run the Google URL Email Scraper with the specific URL
"""

import sys
from google_url_scraper import GoogleURLEmailScraper

def main():
    """Main function to run the Google URL scraper"""
    
    # Your specific Google search URL
    google_url = "https://www.google.com/search?q=@gmail.com,+@<EMAIL><EMAIL>+huang,+hyun,+wei+txt+2025&sca_esv=e7bf22627bcd1c5c&sxsrf=AE3TifNnUr3tV4AY0Aw_D1qB8yJA_rXNGA:1757431062814&ei=FkXAaOq-MamAhbIPz_uG-QM&start=60&sa=N&sstk=Ac65TH6TYToOn1W4LVNuOKsO2eTNSaF_lghNz8xgFUWd37AoHg2sGux2ExZ2jB_VagKJ7t1o7QLsnE6jnwEoloJnj4nEd-u2BJo2EER5lTf-HrXdgv3PFgpInQ7CqZ1KN3E9dKxwNn7IugSpiDk2-U9C3VikKtPSdq0jMV46GlNptDt1EyG1Kv1N9XIl4Paejcow8MNWyiQtmKrS2EKgmxOXHC2zvygg&ved=2ahUKEwjqod3x_MuPAxUpQEEAHc-9IT84KBDy0wN6BAgJEA8&biw=1280&bih=673&dpr=2"
    
    print("🔍 Google URL Email Scraper")
    print("=" * 80)
    print(f"Target URL: {google_url}")
    print("=" * 80)
    
    # Configuration
    max_pages = input("Maximum number of Google search pages to process (default 10): ").strip()
    try:
        max_pages = int(max_pages) if max_pages else 10
    except ValueError:
        max_pages = 10
    
    delay_min = input("Minimum delay between requests in seconds (default 2): ").strip()
    try:
        delay_min = float(delay_min) if delay_min else 2.0
    except ValueError:
        delay_min = 2.0
    
    delay_max = input("Maximum delay between requests in seconds (default 5): ").strip()
    try:
        delay_max = float(delay_max) if delay_max else 5.0
    except ValueError:
        delay_max = 5.0
    
    print(f"\n🚀 Starting scraper with configuration:")
    print(f"   - Max pages: {max_pages}")
    print(f"   - Delay range: {delay_min}-{delay_max} seconds")
    print(f"   - Target domains: gmail.com, 163.com, qq.com, 128.com")
    print(f"   - Target names: huang, hyun, wei")
    print("-" * 80)
    
    # Initialize scraper
    scraper = GoogleURLEmailScraper(delay_range=(delay_min, delay_max))
    
    try:
        # Run the scraper
        print("🔄 Starting email extraction...")
        results = scraper.scrape_google_url(
            google_url=google_url,
            max_pages=max_pages
        )
        
        print("\n✅ SCRAPING COMPLETED!")
        print("=" * 80)
        print(f"📧 Total emails found: {results['count']}")
        print(f"📄 Pages processed: {results['pages_processed']}")
        
        if results['count'] > 0:
            print(f"\n💾 Files saved:")
            print(f"   - Text file: {results.get('txt_file', 'N/A')}")
            print(f"   - CSV file: {results.get('csv_file', 'N/A')}")
            
            # Display all emails found
            print(f"\n📋 All emails found:")
            for i, email in enumerate(sorted(results['emails']), 1):
                print(f"   {i:3d}. {email}")
            
            # Domain breakdown
            domains = {}
            for email in results['emails']:
                domain = email.split('@')[1] if '@' in email else 'unknown'
                domains[domain] = domains.get(domain, 0) + 1
            
            print(f"\n🏢 Domain breakdown:")
            for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True):
                print(f"   - {domain}: {count} emails")
            
            # Target domain analysis
            target_domains = ['gmail.com', '163.com', 'qq.com', '128.com']
            target_emails = [email for email in results['emails'] 
                           if any(domain in email for domain in target_domains)]
            
            print(f"\n🎯 Target domain emails ({len(target_emails)} found):")
            for email in sorted(target_emails):
                print(f"   - {email}")
            
            # Name analysis
            target_names = ['huang', 'hyun', 'wei']
            name_emails = [email for email in results['emails'] 
                          if any(name in email.lower() for name in target_names)]
            
            if name_emails:
                print(f"\n👤 Emails with target names ({len(name_emails)} found):")
                for email in sorted(name_emails):
                    print(f"   - {email}")
        
        else:
            print("\n❌ No emails found. This could be due to:")
            print("   - Google blocking the requests")
            print("   - No results containing the target email patterns")
            print("   - Network connectivity issues")
            print("   - Anti-bot protection on target websites")
            
            print("\n💡 Suggestions:")
            print("   - Try running the scraper again later")
            print("   - Use different search terms")
            print("   - Check if the Google URL is still valid")
        
    except KeyboardInterrupt:
        print("\n⚠️ Scraping interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during scraping: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
