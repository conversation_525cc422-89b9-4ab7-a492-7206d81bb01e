#!/usr/bin/env python3
"""
Google URL Email Scraper
Scrapes email addresses from a specific Google search URL and visits each result page
"""

import requests
import re
import time
import random
import csv
from urllib.parse import urljoin, urlparse, unquote
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
import cloudscraper
import logging
from datetime import datetime
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('google_url_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GoogleURLEmailScraper:
    def __init__(self, delay_range=(2, 5)):
        """
        Initialize the Google URL Email Scraper
        
        Args:
            delay_range (tuple): Min and max delay between requests
        """
        self.ua = UserAgent()
        self.session = requests.Session()
        self.cloudscraper = cloudscraper.create_scraper()
        self.delay_range = delay_range
        self.emails_found = set()
        self.visited_urls = set()
        
        # Comprehensive email regex pattern
        self.email_pattern = re.compile(
            r'\b[A-Za-z0-9]([A-Za-z0-9._%-]*[A-Za-z0-9])?@[A-Za-z0-9]([A-Za-z0-9.-]*[A-Za-z0-9])?\.[A-Za-z]{2,}\b'
        )
        
        # Target email domains from the search query
        self.target_domains = ['gmail.com', '163.com', 'qq.com', '128.com']
        
        # Setup output directory
        self.output_dir = "scraped_emails"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def get_random_headers(self):
        """Generate random headers to appear as different users"""
        return {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
        }
    
    def random_delay(self):
        """Add random delay between requests"""
        delay = random.uniform(*self.delay_range)
        logger.info(f"Waiting {delay:.1f} seconds...")
        time.sleep(delay)
    
    def extract_emails_from_text(self, text, source_url=""):
        """Extract email addresses from text with focus on target domains"""
        emails = self.email_pattern.findall(text)
        valid_emails = []
        
        for email_match in emails:
            # Handle tuple results from regex groups
            if isinstance(email_match, tuple):
                email = ''.join(email_match)
            else:
                email = email_match
            
            email = email.lower().strip()
            
            # Check if email contains target domains or Chinese names
            if (any(domain in email for domain in self.target_domains) or
                any(name in email for name in ['huang', 'hyun', 'wei']) or
                '@' in email):
                
                # Additional validation
                if len(email) >= 5 and len(email) <= 254 and email.count('@') == 1:
                    valid_emails.append(email)
                    logger.info(f"Found target email: {email} from {source_url}")
        
        return valid_emails
    
    def scrape_with_requests(self, url, timeout=15):
        """Scrape URL using requests with header rotation"""
        try:
            headers = self.get_random_headers()
            
            response = self.session.get(
                url, 
                headers=headers, 
                timeout=timeout,
                allow_redirects=True
            )
            response.raise_for_status()
            return response.text
            
        except Exception as e:
            logger.warning(f"Requests failed for {url}: {e}")
            return None
    
    def scrape_with_cloudscraper(self, url, timeout=15):
        """Scrape URL using cloudscraper to bypass Cloudflare"""
        try:
            response = self.cloudscraper.get(url, timeout=timeout)
            return response.text
        except Exception as e:
            logger.warning(f"Cloudscraper failed for {url}: {e}")
            return None
    
    def scrape_with_selenium(self, url, timeout=20):
        """Scrape URL using undetected Chrome driver"""
        driver = None
        try:
            options = uc.ChromeOptions()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            options.add_argument(f'--user-agent={self.ua.random}')
            
            driver = uc.Chrome(options=options)
            driver.set_page_load_timeout(timeout)
            driver.get(url)
            
            # Wait for page to load
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            return driver.page_source
            
        except Exception as e:
            logger.warning(f"Selenium failed for {url}: {e}")
            return None
        finally:
            if driver:
                driver.quit()
    
    def extract_links_from_google_results(self, html_content):
        """Extract actual website links from Google search results"""
        soup = BeautifulSoup(html_content, 'html.parser')
        links = []
        
        # Multiple selectors for different Google result formats
        selectors = [
            'div.g a[href]',  # Standard results
            'h3 a[href]',     # Title links
            'a[href^="/url?q="]',  # URL redirects
            '.yuRUbf a[href]',  # New Google format
            '.kCrYT a[href]',   # Another format
        ]
        
        for selector in selectors:
            for link in soup.select(selector):
                href = link.get('href', '')
                
                if href.startswith('/url?q='):
                    # Extract actual URL from Google redirect
                    try:
                        actual_url = href.split('/url?q=')[1].split('&')[0]
                        actual_url = unquote(actual_url)
                        if actual_url.startswith('http') and 'google.com' not in actual_url:
                            links.append(actual_url)
                    except (IndexError, ValueError):
                        continue
                elif href.startswith('http') and 'google.com' not in href:
                    links.append(href)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_links = []
        for link in links:
            if link not in seen:
                seen.add(link)
                unique_links.append(link)
        
        return unique_links
    
    def scrape_page_for_emails(self, url):
        """Scrape a single page for email addresses using multiple methods"""
        if url in self.visited_urls:
            return []
        
        self.visited_urls.add(url)
        logger.info(f"Scraping: {url}")
        
        # Try multiple scraping methods
        methods = [
            self.scrape_with_requests,
            self.scrape_with_cloudscraper,
            self.scrape_with_selenium
        ]
        
        for method in methods:
            try:
                html_content = method(url)
                if html_content:
                    emails = self.extract_emails_from_text(html_content, url)
                    if emails:
                        logger.info(f"Found {len(emails)} emails on {url}")
                        return emails
                    break
            except Exception as e:
                logger.warning(f"Method {method.__name__} failed for {url}: {e}")
                continue
        
        return []
    
    def get_next_page_url(self, current_url, html_content):
        """Extract next page URL from Google search results"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Look for "Next" button
        next_links = soup.find_all('a', {'aria-label': 'Next page'})
        if not next_links:
            next_links = soup.find_all('a', string=re.compile(r'Next|下一页'))
        
        for link in next_links:
            href = link.get('href')
            if href:
                if href.startswith('/search'):
                    return f"https://www.google.com{href}"
                elif href.startswith('http'):
                    return href
        
        return None
    
    def scrape_google_url(self, google_url, max_pages=10):
        """
        Main method to scrape emails from Google search URL and visit each result page
        
        Args:
            google_url (str): The Google search URL to start from
            max_pages (int): Maximum number of Google search pages to process
        """
        logger.info(f"Starting Google URL scraping from: {google_url}")
        logger.info(f"Will process up to {max_pages} search result pages")
        
        all_emails = set()
        current_url = google_url
        page_count = 0
        
        while current_url and page_count < max_pages:
            page_count += 1
            logger.info(f"Processing Google search page {page_count}/{max_pages}")
            logger.info(f"URL: {current_url}")
            
            # Scrape Google search results page
            search_html = self.scrape_with_requests(current_url)
            if not search_html:
                search_html = self.scrape_with_cloudscraper(current_url)
            if not search_html:
                search_html = self.scrape_with_selenium(current_url)
            
            if not search_html:
                logger.warning(f"Failed to scrape search page: {current_url}")
                break
            
            # Extract emails from the search results page itself
            search_emails = self.extract_emails_from_text(search_html, current_url)
            if search_emails:
                all_emails.update(search_emails)
                logger.info(f"Found {len(search_emails)} emails on search page {page_count}")
            
            # Extract links from search results
            result_links = self.extract_links_from_google_results(search_html)
            logger.info(f"Found {len(result_links)} result links on search page {page_count}")
            
            # Scrape each result page for emails
            for link_num, link in enumerate(result_links, 1):
                try:
                    logger.info(f"Processing result link {link_num}/{len(result_links)} from page {page_count}")
                    emails = self.scrape_page_for_emails(link)
                    
                    if emails:
                        all_emails.update(emails)
                        logger.info(f"Total unique emails found so far: {len(all_emails)}")
                    
                    # Random delay between requests
                    self.random_delay()
                    
                except Exception as e:
                    logger.error(f"Error processing link {link}: {e}")
                    continue
            
            # Get next page URL
            next_url = self.get_next_page_url(current_url, search_html)
            if next_url:
                current_url = next_url
                logger.info(f"Found next page URL: {next_url}")
                # Longer delay between search pages
                time.sleep(random.uniform(5, 10))
            else:
                logger.info("No more search pages found")
                break
        
        logger.info(f"Scraping completed! Found {len(all_emails)} unique emails")
        
        # Save results
        if all_emails:
            txt_file = self.save_emails_to_file(all_emails)
            csv_file = self.save_emails_to_csv(all_emails)
            
            return {
                'emails': list(all_emails),
                'count': len(all_emails),
                'txt_file': txt_file,
                'csv_file': csv_file,
                'pages_processed': page_count
            }
        else:
            logger.warning("No emails found!")
            return {'emails': [], 'count': 0, 'pages_processed': page_count}
    
    def save_emails_to_file(self, emails, filename=None):
        """Save emails to a text file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.output_dir}/google_url_emails_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            for email in sorted(emails):
                f.write(f"{email}\n")
        
        logger.info(f"Saved {len(emails)} emails to {filename}")
        return filename
    
    def save_emails_to_csv(self, emails, filename=None):
        """Save emails to a CSV file with additional metadata"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.output_dir}/google_url_emails_{timestamp}.csv"
        
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Email', 'Domain', 'Timestamp'])
            
            for email in sorted(emails):
                domain = email.split('@')[1] if '@' in email else ''
                timestamp = datetime.now().isoformat()
                writer.writerow([email, domain, timestamp])
        
        logger.info(f"Saved {len(emails)} emails to CSV: {filename}")
        return filename
