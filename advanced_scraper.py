#!/usr/bin/env python3
"""
Advanced Google Email Scraper with Async Support and Enhanced Features
"""

import asyncio
import aiohttp
import httpx
import json
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set, Optional, Tuple
import logging
from urllib.parse import urljoin, urlparse, quote_plus
from bs4 import BeautifulSoup
import re
from fake_useragent import UserAgent
import random
from config import *

# Setup logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdvancedEmailScraper:
    def __init__(self, use_proxy: bool = False, proxy_list: List[str] = None):
        """
        Initialize the Advanced Email Scraper
        
        Args:
            use_proxy: Whether to use proxy rotation
            proxy_list: List of proxy servers
        """
        self.ua = UserAgent()
        self.use_proxy = use_proxy
        self.proxy_list = proxy_list or PROXY_LIST
        self.emails_found: Set[str] = set()
        self.visited_urls: Set[str] = set()
        self.failed_urls: Set[str] = set()
        self.source_mapping: Dict[str, str] = {}  # email -> source URL
        
        # Email regex pattern (more comprehensive)
        self.email_pattern = re.compile(
            r'\b[A-Za-z0-9]([A-Za-z0-9._%-]*[A-Za-z0-9])?@[A-Za-z0-9]([A-Za-z0-9.-]*[A-Za-z0-9])?\.[A-Za-z]{2,}\b'
        )
        
        # Setup output directory
        self.output_dir = Path(DEFAULT_OUTPUT_DIR)
        self.output_dir.mkdir(exist_ok=True)
        
        # Statistics
        self.stats = {
            'start_time': None,
            'end_time': None,
            'pages_scraped': 0,
            'pages_failed': 0,
            'emails_found': 0,
            'unique_domains': 0,
            'total_requests': 0,
        }
    
    def get_random_headers(self) -> Dict[str, str]:
        """Generate random headers"""
        headers = REQUEST_HEADERS['default'].copy()
        headers['User-Agent'] = random.choice(CUSTOM_USER_AGENTS) if CUSTOM_USER_AGENTS else self.ua.random
        return headers
    
    def get_random_proxy(self) -> Optional[str]:
        """Get a random proxy from the proxy list"""
        if self.use_proxy and self.proxy_list:
            return random.choice(self.proxy_list)
        return None
    
    def is_valid_email(self, email: str) -> bool:
        """Validate email address"""
        email = email.lower().strip()
        
        # Basic length check
        if len(email) < MIN_EMAIL_LENGTH or len(email) > MAX_EMAIL_LENGTH:
            return False
        
        # Check for excluded patterns
        if any(pattern in email for pattern in EXCLUDED_EMAIL_PATTERNS):
            return False
        
        # Extract domain
        if '@' not in email:
            return False
        
        domain = email.split('@')[1]
        
        # Check if domain is in valid list or has valid TLD
        if (domain in VALID_EMAIL_DOMAINS or 
            any(tld in domain for tld in ['.com', '.org', '.net', '.edu', '.gov', '.co.uk', '.de', '.fr'])):
            return True
        
        return False
    
    def extract_emails_from_text(self, text: str, source_url: str = "") -> List[str]:
        """Extract and validate email addresses from text"""
        emails = self.email_pattern.findall(text)
        valid_emails = []
        
        for email_match in emails:
            # Handle tuple results from regex groups
            if isinstance(email_match, tuple):
                email = ''.join(email_match)
            else:
                email = email_match
            
            if self.is_valid_email(email):
                email = email.lower().strip()
                valid_emails.append(email)
                if source_url:
                    self.source_mapping[email] = source_url
        
        return valid_emails
    
    async def scrape_with_aiohttp(self, session: aiohttp.ClientSession, url: str) -> Optional[str]:
        """Scrape URL using aiohttp"""
        try:
            headers = self.get_random_headers()
            proxy = self.get_random_proxy()
            
            async with session.get(
                url, 
                headers=headers, 
                proxy=proxy,
                timeout=aiohttp.ClientTimeout(total=DEFAULT_TIMEOUT)
            ) as response:
                if response.status == 200:
                    return await response.text()
                else:
                    logger.warning(f"HTTP {response.status} for {url}")
                    return None
                    
        except Exception as e:
            logger.warning(f"aiohttp failed for {url}: {e}")
            return None
    
    async def scrape_with_httpx(self, client: httpx.AsyncClient, url: str) -> Optional[str]:
        """Scrape URL using httpx"""
        try:
            headers = self.get_random_headers()
            proxies = self.get_random_proxy()
            
            response = await client.get(
                url,
                headers=headers,
                proxies=proxies,
                timeout=DEFAULT_TIMEOUT
            )
            response.raise_for_status()
            return response.text
            
        except Exception as e:
            logger.warning(f"httpx failed for {url}: {e}")
            return None
    
    def get_google_search_urls(self, query: str, num_pages: int = 5) -> List[str]:
        """Generate Google search URLs for multiple pages"""
        urls = []
        encoded_query = quote_plus(query)
        base_domain = random.choice(GOOGLE_SEARCH_DOMAINS)
        
        for page in range(num_pages):
            start = page * 10
            url = f"{base_domain}/search?q={encoded_query}&start={start}&num=10"
            urls.append(url)
        
        return urls
    
    def extract_links_from_google_results(self, html_content: str) -> List[str]:
        """Extract actual website links from Google search results"""
        soup = BeautifulSoup(html_content, 'html.parser')
        links = []
        
        # Multiple selectors for different Google result formats
        selectors = [
            'div.g a[href]',  # Standard results
            'h3 a[href]',     # Title links
            'a[href^="/url?q="]',  # URL redirects
        ]
        
        for selector in selectors:
            for link in soup.select(selector):
                href = link.get('href', '')
                
                if href.startswith('/url?q='):
                    # Extract actual URL from Google redirect
                    try:
                        actual_url = href.split('/url?q=')[1].split('&')[0]
                        if actual_url.startswith('http') and 'google.com' not in actual_url:
                            links.append(actual_url)
                    except IndexError:
                        continue
                elif href.startswith('http') and 'google.com' not in href:
                    links.append(href)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_links = []
        for link in links:
            if link not in seen:
                seen.add(link)
                unique_links.append(link)
        
        return unique_links
    
    async def scrape_page_for_emails(self, session: aiohttp.ClientSession, url: str) -> List[str]:
        """Scrape a single page for email addresses"""
        if url in self.visited_urls or url in self.failed_urls:
            return []
        
        self.visited_urls.add(url)
        self.stats['total_requests'] += 1
        
        logger.info(f"Scraping: {url}")
        
        # Try scraping with aiohttp
        html_content = await self.scrape_with_aiohttp(session, url)
        
        if html_content:
            emails = self.extract_emails_from_text(html_content, url)
            if emails:
                self.stats['pages_scraped'] += 1
                logger.info(f"Found {len(emails)} emails on {url}")
                return emails
            else:
                self.stats['pages_scraped'] += 1
        else:
            self.failed_urls.add(url)
            self.stats['pages_failed'] += 1
        
        return []
    
    async def scrape_google_search_async(
        self, 
        query: str, 
        num_pages: int = 3, 
        max_links_per_page: int = 5,
        concurrent_requests: int = CONCURRENT_REQUESTS
    ) -> Dict:
        """
        Asynchronously scrape emails from Google search results
        """
        self.stats['start_time'] = datetime.now()
        logger.info(f"Starting async scraping for query: '{query}'")
        
        all_emails = set()
        
        # Create aiohttp session
        connector = aiohttp.TCPConnector(limit=concurrent_requests)
        timeout = aiohttp.ClientTimeout(total=DEFAULT_TIMEOUT)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Get Google search URLs
            search_urls = self.get_google_search_urls(query, num_pages)
            
            for page_num, search_url in enumerate(search_urls, 1):
                logger.info(f"Processing search page {page_num}/{num_pages}")
                
                # Scrape Google search results page
                search_html = await self.scrape_with_aiohttp(session, search_url)
                
                if not search_html:
                    logger.warning(f"Failed to scrape search page: {search_url}")
                    continue
                
                # Extract links from search results
                result_links = self.extract_links_from_google_results(search_html)
                logger.info(f"Found {len(result_links)} links on search page {page_num}")
                
                # Limit links per page
                result_links = result_links[:max_links_per_page]
                
                # Create tasks for concurrent scraping
                tasks = []
                for link in result_links:
                    task = self.scrape_page_for_emails(session, link)
                    tasks.append(task)
                
                # Execute tasks concurrently
                if tasks:
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    for result in results:
                        if isinstance(result, list):
                            all_emails.update(result)
                        elif isinstance(result, Exception):
                            logger.error(f"Task failed: {result}")
                
                logger.info(f"Total unique emails found so far: {len(all_emails)}")
                
                # Delay between search pages
                await asyncio.sleep(random.uniform(2, 4))
        
        self.stats['end_time'] = datetime.now()
        self.stats['emails_found'] = len(all_emails)
        self.stats['unique_domains'] = len(set(email.split('@')[1] for email in all_emails))
        
        logger.info(f"Async scraping completed! Found {len(all_emails)} unique emails")
        
        # Save results
        if all_emails:
            self.emails_found = all_emails
            files_saved = await self.save_results_async(all_emails)
            
            return {
                'emails': list(all_emails),
                'count': len(all_emails),
                'stats': self.stats,
                'files': files_saved
            }
        else:
            logger.warning("No emails found!")
            return {'emails': [], 'count': 0, 'stats': self.stats}
    
    async def save_results_async(self, emails: Set[str]) -> Dict[str, str]:
        """Save results to multiple formats asynchronously"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        files_saved = {}
        
        # Save to TXT
        if 'txt' in OUTPUT_FORMATS:
            txt_file = self.output_dir / f"emails_{timestamp}.txt"
            with open(txt_file, 'w', encoding='utf-8') as f:
                for email in sorted(emails):
                    f.write(f"{email}\n")
            files_saved['txt'] = str(txt_file)
        
        # Save to CSV with metadata
        if 'csv' in OUTPUT_FORMATS:
            csv_file = self.output_dir / f"emails_{timestamp}.csv"
            import csv
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                headers = ['Email', 'Domain', 'Source_URL', 'Timestamp']
                writer.writerow(headers)
                
                for email in sorted(emails):
                    domain = email.split('@')[1]
                    source_url = self.source_mapping.get(email, 'Unknown')
                    timestamp_str = datetime.now().isoformat()
                    writer.writerow([email, domain, source_url, timestamp_str])
            files_saved['csv'] = str(csv_file)
        
        # Save to JSON with full metadata
        if 'json' in OUTPUT_FORMATS:
            json_file = self.output_dir / f"emails_{timestamp}.json"
            data = {
                'metadata': {
                    'scraping_timestamp': datetime.now().isoformat(),
                    'total_emails': len(emails),
                    'statistics': self.stats,
                },
                'emails': [
                    {
                        'email': email,
                        'domain': email.split('@')[1],
                        'source_url': self.source_mapping.get(email, 'Unknown')
                    }
                    for email in sorted(emails)
                ]
            }
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            files_saved['json'] = str(json_file)
        
        logger.info(f"Results saved to {len(files_saved)} files")
        return files_saved
