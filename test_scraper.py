#!/usr/bin/env python3
"""
Test script for the Google Email Scraper
"""

import asyncio
import sys
from pathlib import Path
from google_email_scraper import GoogleEmailScraper
from advanced_scraper import AdvancedEmailScraper

def test_email_extraction():
    """Test email extraction functionality"""
    print("🧪 Testing email extraction...")
    
    scraper = GoogleEmailScraper()
    
    # Test HTML content with emails
    test_html = """
    <html>
    <body>
        <p>Contact <NAME_EMAIL> or <EMAIL></p>
        <div>Sales: <EMAIL></div>
        <span>Invalid: <EMAIL></span>
        <a href="mailto:<EMAIL>">Email us</a>
    </body>
    </html>
    """
    
    emails = scraper.extract_emails_from_text(test_html)
    print(f"✅ Found {len(emails)} valid emails: {emails}")
    
    return len(emails) > 0

def test_google_search_url_generation():
    """Test Google search URL generation"""
    print("\n🧪 Testing Google search URL generation...")
    
    scraper = GoogleEmailScraper()
    urls = scraper.get_google_search_urls("test query", num_pages=3)
    
    print(f"✅ Generated {len(urls)} search URLs:")
    for i, url in enumerate(urls, 1):
        print(f"   {i}. {url}")
    
    return len(urls) == 3

def test_user_agent_rotation():
    """Test user agent rotation"""
    print("\n🧪 Testing user agent rotation...")
    
    scraper = GoogleEmailScraper()
    
    # Generate multiple headers
    headers_list = []
    for i in range(5):
        headers = scraper.get_random_headers()
        headers_list.append(headers['User-Agent'])
    
    unique_agents = set(headers_list)
    print(f"✅ Generated {len(unique_agents)} unique user agents out of 5 requests")
    
    return len(unique_agents) >= 1

async def test_async_scraper():
    """Test async scraper functionality"""
    print("\n🧪 Testing async scraper...")
    
    scraper = AdvancedEmailScraper()
    
    # Test email validation
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",  # Should be filtered out
        "invalid-email",        # Invalid format
        "<EMAIL>"
    ]
    
    valid_emails = []
    for email in test_emails:
        if scraper.is_valid_email(email):
            valid_emails.append(email)
    
    print(f"✅ Validated {len(valid_emails)} emails out of {len(test_emails)}: {valid_emails}")
    
    return len(valid_emails) >= 2

def test_configuration():
    """Test configuration loading"""
    print("\n🧪 Testing configuration...")
    
    try:
        from config import PROXY_LIST, VALID_EMAIL_DOMAINS, DEFAULT_DELAY_RANGE
        print("✅ Configuration loaded successfully")
        print(f"   - Proxy list: {len(PROXY_LIST)} proxies configured")
        print(f"   - Valid domains: {len(VALID_EMAIL_DOMAINS)} domains")
        print(f"   - Delay range: {DEFAULT_DELAY_RANGE}")
        return True
    except ImportError as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_output_directory():
    """Test output directory creation"""
    print("\n🧪 Testing output directory...")
    
    scraper = GoogleEmailScraper()
    output_dir = Path(scraper.output_dir)
    
    if output_dir.exists():
        print(f"✅ Output directory exists: {output_dir}")
        return True
    else:
        print(f"❌ Output directory not found: {output_dir}")
        return False

def test_file_saving():
    """Test file saving functionality"""
    print("\n🧪 Testing file saving...")
    
    scraper = GoogleEmailScraper()
    test_emails = ["<EMAIL>", "<EMAIL>"]
    
    try:
        # Test TXT saving
        txt_file = scraper.save_emails_to_file(test_emails, "test_emails.txt")
        
        # Test CSV saving  
        csv_file = scraper.save_emails_to_csv(test_emails, "test_emails.csv")
        
        # Check if files exist
        txt_exists = Path(txt_file).exists()
        csv_exists = Path(csv_file).exists()
        
        print(f"✅ TXT file saved: {txt_exists}")
        print(f"✅ CSV file saved: {csv_exists}")
        
        # Clean up test files
        if txt_exists:
            Path(txt_file).unlink()
        if csv_exists:
            Path(csv_file).unlink()
        
        return txt_exists and csv_exists
        
    except Exception as e:
        print(f"❌ File saving error: {e}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Google Email Scraper Tests")
    print("=" * 60)
    
    tests = [
        ("Email Extraction", test_email_extraction),
        ("Google Search URLs", test_google_search_url_generation),
        ("User Agent Rotation", test_user_agent_rotation),
        ("Configuration Loading", test_configuration),
        ("Output Directory", test_output_directory),
        ("File Saving", test_file_saving),
        ("Async Scraper", test_async_scraper),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
                
        except Exception as e:
            print(f"❌ {test_name} test error: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The scraper is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the configuration and dependencies.")
        return False

def main():
    """Main test function"""
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected test error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
