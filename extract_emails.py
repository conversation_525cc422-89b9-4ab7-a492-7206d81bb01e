#!/usr/bin/env python3
"""
Email extraction from the Google search response
"""

import re
from datetime import datetime

def extract_emails_from_text(text):
    """Extract email addresses from text"""
    # Comprehensive email regex pattern
    email_pattern = re.compile(
        r'\b[A-Za-z0-9]([A-Za-z0-9._%-]*[A-Za-z0-9])?@[A-Za-z0-9]([A-Za-z0-9.-]*[A-Za-z0-9])?\.[A-Za-z]{2,}\b'
    )
    
    emails = email_pattern.findall(text)
    valid_emails = []
    
    # Target domains from the search query
    target_domains = ['gmail.com', '163.com', 'qq.com', '128.com']
    target_names = ['huang', 'hyun', 'wei']
    
    for email_match in emails:
        # Handle tuple results from regex groups
        if isinstance(email_match, tuple):
            email = ''.join(email_match)
        else:
            email = email_match
        
        email = email.lower().strip()
        
        # Check if email contains target domains or Chinese names
        if (any(domain in email for domain in target_domains) or
            any(name in email for name in target_names) or
            '@' in email):
            
            # Additional validation
            if len(email) >= 5 and len(email) <= 254 and email.count('@') == 1:
                valid_emails.append(email)
    
    return valid_emails

def save_emails_to_file(emails, filename="extracted_emails.txt"):
    """Save emails to a text file"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"# Emails extracted on {datetime.now().isoformat()}\n")
        f.write(f"# Total emails found: {len(emails)}\n\n")
        for email in sorted(set(emails)):
            f.write(f"{email}\n")
    
    print(f"Saved {len(set(emails))} unique emails to {filename}")
    return filename

def main():
    """Extract emails from the Google search response"""
    
    # The response content from the Google URL
    google_response = """Google Searchbody{background-color:#fff}window.google = window.google || {};window.google.c = window.google.c || {ezx:false,cap:0};table,div,span,p{display:none}Please click here if you are not redirected within a few seconds.(function(){var sctm=false;(function(){sctm&&google.tick("load","pbsst");}).call(this);})();//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjogMywic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiICJdLCJuYW1lcyI6WyJjbG9zdXJlRHluYW1pY0J1dHRvbiJdLCJtYXBwaW5ncyI6IkFBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEifQ=="""
    
    print("🔍 Extracting emails from Google search response...")
    print("=" * 60)
    
    # Extract emails from the response
    emails = extract_emails_from_text(google_response)
    
    if emails:
        print(f"✅ Found {len(emails)} emails:")
        for i, email in enumerate(emails, 1):
            print(f"   {i}. {email}")
        
        # Save to file
        filename = save_emails_to_file(emails)
        print(f"\n💾 Results saved to: {filename}")
        
    else:
        print("❌ No emails found in the response.")
        print("\n🔍 The Google URL appears to be returning a redirect page.")
        print("This is likely due to:")
        print("   - Anti-bot protection")
        print("   - Expired search session")
        print("   - Geographic restrictions")
        
        print("\n💡 Suggestions:")
        print("   1. Try accessing the URL directly in a browser")
        print("   2. Use a fresh Google search")
        print("   3. Try different search terms")
        print("   4. Use proxy servers")
    
    # Let's also try to extract any potential email patterns from the JavaScript code
    print("\n🔍 Searching for any encoded email patterns...")
    
    # Look for base64 encoded content that might contain emails
    base64_pattern = re.compile(r'[A-Za-z0-9+/]{20,}={0,2}')
    base64_matches = base64_pattern.findall(google_response)
    
    if base64_matches:
        print(f"Found {len(base64_matches)} base64-like strings")
        
        # Try to decode and search for emails
        import base64
        decoded_emails = []
        
        for b64_str in base64_matches[:5]:  # Check first 5 matches
            try:
                decoded = base64.b64decode(b64_str + '==').decode('utf-8', errors='ignore')
                found_emails = extract_emails_from_text(decoded)
                decoded_emails.extend(found_emails)
            except:
                continue
        
        if decoded_emails:
            print(f"✅ Found {len(decoded_emails)} emails in decoded content:")
            for email in decoded_emails:
                print(f"   - {email}")
            
            all_emails = emails + decoded_emails
            filename = save_emails_to_file(all_emails, "all_extracted_emails.txt")
        else:
            print("❌ No emails found in decoded base64 content")
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print(f"   - Direct emails found: {len(emails)}")
    print(f"   - Base64 decoded emails: {len(decoded_emails) if 'decoded_emails' in locals() else 0}")
    print(f"   - Total unique emails: {len(set(emails + (decoded_emails if 'decoded_emails' in locals() else [])))}")

if __name__ == "__main__":
    main()
